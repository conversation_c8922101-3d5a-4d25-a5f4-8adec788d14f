<?php

namespace App\Service;

use App\Contracts\WebhookableEvent;
use Illuminate\Support\Facades\Log;

/**
 * Webhook 事件管理器
 * 提供事件注册、发现和管理功能
 */
class WebhookEventManager
{
    /**
     * 已注册的事件类型映射
     */
    private static array $eventTypes = [];

    /**
     * 注册事件类型
     */
    public static function registerEventType(string $eventType, string $eventClass, array $metadata = []): void
    {
        if (!is_subclass_of($eventClass, WebhookableEvent::class)) {
            throw new \InvalidArgumentException("Event class {$eventClass} must implement WebhookableEvent interface");
        }

        self::$eventTypes[$eventType] = [
            'class' => $eventClass,
            'metadata' => $metadata,
        ];
    }

    /**
     * 获取所有已注册的事件类型
     */
    public static function getRegisteredEventTypes(): array
    {
        return array_keys(self::$eventTypes);
    }

    /**
     * 获取事件类型的详细信息
     */
    public static function getEventTypeInfo(string $eventType): ?array
    {
        return self::$eventTypes[$eventType] ?? null;
    }

    /**
     * 自动发现并注册事件类型
     */
    public static function discoverEventTypes(): void
    {
        // K8s 事件类型
        $k8sResourceTypes = ['deployment', 'statefulset', 'service', 'ingress', 'pod', 'secret', 'configmap', 'persistentvolumeclaim', 'horizontalpodautoscaler', 'event'];
        $k8sActions = ['created', 'updated', 'deleted'];

        foreach ($k8sResourceTypes as $resourceType) {
            foreach ($k8sActions as $action) {
                $eventType = "{$resourceType}.{$action}";
                self::registerEventType($eventType, 'App\Events\K8s\BaseK8sResourceEvent', [
                    'category' => 'k8s',
                    'resource_type' => $resourceType,
                    'action' => $action,
                    'description' => "Kubernetes {$resourceType} {$action} event",
                ]);
            }
        }

        // 用户相关事件
        self::registerEventType('user.registered', 'App\Events\User\UserRegistered', [
            'category' => 'user',
            'description' => 'User registration event',
        ]);

        // 计费相关事件
        self::registerEventType('billing.payment_processed', 'App\Events\Billing\PaymentProcessed', [
            'category' => 'billing',
            'description' => 'Payment processing event',
        ]);

        // 可以继续添加其他事件类型...
    }

    /**
     * 获取按分类组织的事件类型
     */
    public static function getEventTypesByCategory(): array
    {
        $categories = [];
        
        foreach (self::$eventTypes as $eventType => $info) {
            $category = $info['metadata']['category'] ?? 'other';
            $categories[$category][] = [
                'type' => $eventType,
                'description' => $info['metadata']['description'] ?? '',
                'class' => $info['class'],
            ];
        }

        return $categories;
    }

    /**
     * 验证事件类型是否有效
     */
    public static function isValidEventType(string $eventType): bool
    {
        return isset(self::$eventTypes[$eventType]);
    }

    /**
     * 触发事件（用于测试或手动触发）
     */
    public static function dispatchEvent(string $eventType, array $data = []): void
    {
        $eventInfo = self::getEventTypeInfo($eventType);
        
        if (!$eventInfo) {
            throw new \InvalidArgumentException("Unknown event type: {$eventType}");
        }

        $eventClass = $eventInfo['class'];
        
        // 这里需要根据具体的事件类构造参数
        // 实际使用时可能需要更复杂的工厂模式
        Log::info("Dispatching webhook event", [
            'event_type' => $eventType,
            'event_class' => $eventClass,
            'data' => $data,
        ]);
    }
}

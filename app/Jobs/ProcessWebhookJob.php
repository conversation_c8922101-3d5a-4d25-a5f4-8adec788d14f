<?php

namespace App\Jobs;

use App\Models\WebhookDelivery;
use App\Models\WebhookEndpoint;
use App\Service\WebhookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * 异步处理 Webhook 发送的队列任务
 */
class ProcessWebhookJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务最大尝试次数
     */
    public int $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public int $timeout = 60;

    public function __construct(
        private int $webhookEndpointId,
        private string $eventType,
        private array $payload,
        private int $priority = 5
    ) {
        // 根据优先级设置队列
        $this->onQueue($this->getQueueByPriority($priority));
    }

    /**
     * 执行任务
     */
    public function handle(WebhookService $webhookService): void
    {
        $endpoint = WebhookEndpoint::find($this->webhookEndpointId);

        if (!$endpoint) {
            Log::warning('Webhook endpoint not found', [
                'endpoint_id' => $this->webhookEndpointId,
                'event_type' => $this->eventType,
            ]);
            return;
        }

        if (!$endpoint->is_active) {
            Log::info('Webhook endpoint is inactive, skipping', [
                'endpoint_id' => $this->webhookEndpointId,
                'event_type' => $this->eventType,
            ]);
            return;
        }

        try {
            $webhookService->sendWebhook($endpoint, $this->eventType, $this->payload);
            
            Log::info('Webhook job completed successfully', [
                'endpoint_id' => $this->webhookEndpointId,
                'event_type' => $this->eventType,
            ]);
        } catch (\Exception $e) {
            Log::error('Webhook job failed', [
                'endpoint_id' => $this->webhookEndpointId,
                'event_type' => $this->eventType,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
            ]);

            // 重新抛出异常以触发重试
            throw $e;
        }
    }

    /**
     * 任务失败处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Webhook job failed permanently', [
            'endpoint_id' => $this->webhookEndpointId,
            'event_type' => $this->eventType,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }

    /**
     * 根据优先级获取队列名称
     */
    private function getQueueByPriority(int $priority): string
    {
        return match (true) {
            $priority >= 8 => 'webhooks-high',
            $priority >= 5 => 'webhooks-default',
            default => 'webhooks-low',
        };
    }

    /**
     * 获取任务的唯一标识符（防止重复任务）
     */
    public function uniqueId(): string
    {
        return "webhook:{$this->webhookEndpointId}:{$this->eventType}:" . md5(json_encode($this->payload));
    }
}

<?php

namespace App\Console\Commands;

use App\Service\WebhookEventManager;
use Illuminate\Console\Command;

/**
 * 列出所有可用的 Webhook 事件类型
 */
class WebhookEventListCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'webhook:events:list {--category= : Filter by category}';

    /**
     * The console command description.
     */
    protected $description = 'List all available webhook event types';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        // 发现并注册事件类型
        WebhookEventManager::discoverEventTypes();

        $category = $this->option('category');

        if ($category) {
            $this->showEventsByCategory($category);
        } else {
            $this->showAllEvents();
        }

        return 0;
    }

    /**
     * 显示所有事件
     */
    private function showAllEvents(): void
    {
        $eventsByCategory = WebhookEventManager::getEventTypesByCategory();

        $this->info('Available Webhook Event Types:');
        $this->line('');

        foreach ($eventsByCategory as $category => $events) {
            $this->line("<fg=yellow>Category: {$category}</>");
            $this->line(str_repeat('-', 40));

            $tableData = [];
            foreach ($events as $event) {
                $tableData[] = [
                    $event['type'],
                    $event['description'],
                ];
            }

            $this->table(['Event Type', 'Description'], $tableData);
            $this->line('');
        }
    }

    /**
     * 显示特定分类的事件
     */
    private function showEventsByCategory(string $category): void
    {
        $eventsByCategory = WebhookEventManager::getEventTypesByCategory();

        if (!isset($eventsByCategory[$category])) {
            $this->error("Category '{$category}' not found.");
            $this->line('Available categories: ' . implode(', ', array_keys($eventsByCategory)));
            return;
        }

        $events = $eventsByCategory[$category];

        $this->info("Webhook Event Types in category: {$category}");
        $this->line('');

        $tableData = [];
        foreach ($events as $event) {
            $tableData[] = [
                $event['type'],
                $event['description'],
                $event['class'],
            ];
        }

        $this->table(['Event Type', 'Description', 'Class'], $tableData);
    }
}

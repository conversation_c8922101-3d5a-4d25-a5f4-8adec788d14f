<?php

namespace App\Events\Billing;

use App\Contracts\WebhookableEvent;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 支付处理事件
 * 示例计费相关事件
 */
class PaymentProcessed implements WebhookableEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public User $user,
        public string $amount,
        public string $currency,
        public string $paymentMethod,
        public string $transactionId,
        public ?int $workspaceId = null
    ) {}

    /**
     * 获取事件类型名称
     */
    public function getEventType(): string
    {
        return 'billing.payment_processed';
    }

    /**
     * 获取事件的 Webhook 负载数据
     */
    public function getWebhookPayload(): array
    {
        return [
            'event_id' => uniqid(),
            'event_type' => $this->getEventType(),
            'user' => [
                'id' => $this->user->id,
                'name' => $this->user->name,
                'email' => $this->user->email,
            ],
            'payment' => [
                'amount' => $this->amount,
                'currency' => $this->currency,
                'method' => $this->paymentMethod,
                'transaction_id' => $this->transactionId,
            ],
            'workspace_id' => $this->workspaceId,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * 获取事件关联的工作区 ID
     */
    public function getWorkspaceId(): ?int
    {
        return $this->workspaceId;
    }

    /**
     * 获取事件关联的命名空间
     */
    public function getNamespace(): ?string
    {
        return null;
    }

    /**
     * 获取事件优先级
     */
    public function getPriority(): int
    {
        // 支付事件为高优先级
        return 9;
    }
}

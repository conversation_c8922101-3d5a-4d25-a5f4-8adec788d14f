<?php

namespace App\Events\K8s;

use App\Contracts\WebhookableEvent;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

abstract class BaseK8sResourceEvent implements ShouldBroadcast, WebhookableEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $namespace;

    public string $clusterName;

    public int $clusterId;

    public string $resourceType;

    public string $resourceName;

    public array $resource;

    public string $action; // created, updated, deleted

    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceType,
        string $resourceName,
        array $resource,
        string $action
    ) {
        $this->namespace = $namespace;
        $this->clusterName = $clusterName;
        $this->clusterId = $clusterId;
        $this->resourceType = $resourceType;
        $this->resourceName = $resourceName;
        $this->resource = $resource;
        $this->action = $action;

        Log::debug('event fire');
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("workspace.{$this->namespace}.{$this->resourceType}"),
            new PrivateChannel("workspace.{$this->namespace}.resources"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'namespace' => $this->namespace,
            'cluster' => [
                'id' => $this->clusterId,
                'name' => $this->clusterName,
            ],
            'resource_type' => $this->resourceType,
            'resource_name' => $this->resourceName,
            'action' => $this->action,
            'resource' => $this->resource,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return "{$this->resourceType}.{$this->action}";
    }

    // WebhookableEvent 接口实现

    /**
     * 获取事件类型名称
     */
    public function getEventType(): string
    {
        return "{$this->resourceType}.{$this->action}";
    }

    /**
     * 获取事件的 Webhook 负载数据
     */
    public function getWebhookPayload(): array
    {
        return [
            'event_id' => uniqid(),
            'event_type' => $this->getEventType(),
            'namespace' => $this->namespace,
            'cluster' => [
                'id' => $this->clusterId,
                'name' => $this->clusterName,
            ],
            'resource' => [
                'type' => $this->resourceType,
                'name' => $this->resourceName,
                'data' => $this->resource,
            ],
            'action' => $this->action,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * 获取事件关联的工作区 ID
     */
    public function getWorkspaceId(): ?int
    {
        // K8s 事件通过 namespace 关联工作区
        return null;
    }

    /**
     * 获取事件关联的命名空间
     */
    public function getNamespace(): ?string
    {
        return $this->namespace;
    }

    /**
     * 获取事件优先级
     */
    public function getPriority(): int
    {
        // K8s 事件默认为中等优先级
        return 5;
    }
}

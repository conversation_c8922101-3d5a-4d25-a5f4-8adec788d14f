<?php

namespace App\Events\User;

use App\Contracts\WebhookableEvent;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 用户注册事件
 * 示例非 K8s 事件，展示如何扩展 Webhook 系统
 */
class UserRegistered implements WebhookableEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public User $user
    ) {}

    /**
     * 获取事件类型名称
     */
    public function getEventType(): string
    {
        return 'user.registered';
    }

    /**
     * 获取事件的 Webhook 负载数据
     */
    public function getWebhookPayload(): array
    {
        return [
            'event_id' => uniqid(),
            'event_type' => $this->getEventType(),
            'user' => [
                'id' => $this->user->id,
                'name' => $this->user->name,
                'email' => $this->user->email,
                'created_at' => $this->user->created_at->toISOString(),
            ],
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * 获取事件关联的工作区 ID
     */
    public function getWorkspaceId(): ?int
    {
        // 用户注册事件可能不关联特定工作区
        // 或者可以关联用户的默认工作区
        return null;
    }

    /**
     * 获取事件关联的命名空间
     */
    public function getNamespace(): ?string
    {
        return null;
    }

    /**
     * 获取事件优先级
     */
    public function getPriority(): int
    {
        // 用户注册事件为高优先级
        return 8;
    }
}

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreWebhookEndpointRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        // 动态获取可用的事件类型
        $availableEvents = $this->getAvailableEvents();

        // 如果获取失败，使用默认的事件列表
        if (empty($availableEvents)) {
            $availableEvents = [
                '*', 'deployment.created', 'deployment.updated', 'deployment.deleted', 'deployment.scaled',
                'statefulset.created', 'statefulset.updated', 'statefulset.deleted',
                'service.created', 'service.updated', 'service.deleted',
                'pod.created', 'pod.updated', 'pod.deleted',
                'user.registered', 'billing.payment_processed',
            ];
        }

        return [
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:2048',
            'events' => 'required|array|min:1',
            'events.*' => 'string|in:'.implode(',', $availableEvents),
            'headers' => 'nullable|array',
            'headers.*' => 'string|max:1000',
            'is_active' => 'boolean',
        ];
    }

    /**
     * 获取可用的事件类型
     */
    private function getAvailableEvents(): array
    {
        try {
            \App\Service\WebhookEventManager::discoverEventTypes();
            $events = \App\Service\WebhookEventManager::getRegisteredEventTypes();

            // 添加通配符
            array_unshift($events, '*');

            return $events;
        } catch (\Exception $e) {
            // 如果获取失败，返回空数组，使用默认列表
            return [];
        }
    }

    public function messages(): array
    {
        return [
            'name.required' => '名称不能为空',
            'name.max' => '名称长度不能超过255个字符',
            'url.required' => 'URL不能为空',
            'url.url' => 'URL格式不正确',
            'url.max' => 'URL长度不能超过2048个字符',
            'events.required' => '至少需要选择一个事件类型',
            'events.min' => '至少需要选择一个事件类型',
            'events.*.in' => '无效的事件类型',
            'headers.array' => '请求头必须是数组格式',
            'headers.*.max' => '请求头值长度不能超过1000个字符',
        ];
    }
}

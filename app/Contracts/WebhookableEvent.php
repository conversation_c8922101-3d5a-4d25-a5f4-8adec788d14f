<?php

namespace App\Contracts;

/**
 * 定义可发送 Webhook 的事件接口
 * 所有需要发送 Webhook 的事件都应该实现此接口
 */
interface WebhookableEvent
{
    /**
     * 获取事件类型名称
     * 例如：deployment.created, user.registered, billing.charged
     */
    public function getEventType(): string;

    /**
     * 获取事件的 Webhook 负载数据
     */
    public function getWebhookPayload(): array;

    /**
     * 获取事件关联的工作区 ID（如果有）
     * 用于确定哪些 Webhook 端点应该接收此事件
     */
    public function getWorkspaceId(): ?int;

    /**
     * 获取事件关联的命名空间（如果有）
     * 主要用于 K8s 相关事件
     */
    public function getNamespace(): ?string;

    /**
     * 获取事件优先级
     * 用于队列处理时的优先级排序
     */
    public function getPriority(): int;
}

<?php

namespace App\Listeners;

use App\Contracts\WebhookableEvent;
use App\Models\WebhookEndpoint;
use App\Service\WebhookService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * 统一的 Webhook 事件监听器
 * 处理所有实现了 WebhookableEvent 接口的事件
 */
class UniversalWebhookListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        private WebhookService $webhookService
    ) {}

    /**
     * 处理事件
     */
    public function handle(WebhookableEvent $event): void
    {
        try {
            $eventType = $event->getEventType();

            Log::debug('Processing universal webhook event', [
                'event_type' => $eventType,
                'namespace' => $event->getNamespace(),
                'workspace_id' => $event->getWorkspaceId(),
            ]);

            // 查找匹配的 Webhook 端点
            $webhookEndpoints = $this->findMatchingEndpoints($event);

            if ($webhookEndpoints->isEmpty()) {
                Log::debug('No matching webhook endpoints found', [
                    'event_type' => $eventType,
                    'namespace' => $event->getNamespace(),
                    'workspace_id' => $event->getWorkspaceId(),
                ]);
                return;
            }

            // 为每个匹配的端点发送 Webhook
            foreach ($webhookEndpoints as $endpoint) {
                if ($this->shouldSendToEndpoint($endpoint, $eventType)) {
                    Log::debug('Sending webhook for event', [
                        'endpoint_id' => $endpoint->id,
                        'endpoint_name' => $endpoint->name,
                        'event_type' => $eventType,
                    ]);

                    $this->webhookService->sendWebhook(
                        $endpoint,
                        $eventType,
                        $event->getWebhookPayload()
                    );
                }
            }

        } catch (\Exception $e) {
            Log::error('Error processing universal webhook event', [
                'error' => $e->getMessage(),
                'event_type' => $event->getEventType(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 查找匹配的 Webhook 端点
     */
    private function findMatchingEndpoints(WebhookableEvent $event): \Illuminate\Database\Eloquent\Collection
    {
        $query = WebhookEndpoint::where('is_active', true);

        // 如果事件有命名空间，通过 workspace 关联查找
        if ($namespace = $event->getNamespace()) {
            $query->whereHas('workspace', function ($q) use ($namespace) {
                $q->where('namespace', $namespace);
            });
        }
        // 如果事件有工作区 ID，直接查找
        elseif ($workspaceId = $event->getWorkspaceId()) {
            $query->where('workspace_id', $workspaceId);
        }
        // 如果都没有，查找全局端点（如果有的话）
        else {
            // 这里可以根据业务需求决定是否支持全局端点
            $query->whereNull('workspace_id');
        }

        return $query->get();
    }

    /**
     * 检查是否应该向端点发送事件
     */
    private function shouldSendToEndpoint(WebhookEndpoint $endpoint, string $eventType): bool
    {
        return $endpoint->shouldReceiveEvent($eventType) || $endpoint->shouldReceiveEvent('*');
    }

    /**
     * 处理任务失败
     */
    public function failed(WebhookableEvent $event, \Throwable $exception): void
    {
        Log::error('Universal webhook listener failed', [
            'event_type' => $event->getEventType(),
            'namespace' => $event->getNamespace(),
            'workspace_id' => $event->getWorkspaceId(),
            'error' => $exception->getMessage(),
        ]);
    }
}

<?php

namespace App\Providers;

use App\Contracts\WebhookableEvent;
use App\Events\K8s\BaseK8sResourceEvent;
use App\Listeners\UniversalWebhookListener;
use App\Listeners\WebhookEventListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        // 新的统一 Webhook 监听器 - 处理所有实现 WebhookableEvent 接口的事件
        WebhookableEvent::class => [
            UniversalWebhookListener::class,
        ],

        // 保留旧的监听器以确保向后兼容（可以逐步移除）
        BaseK8sResourceEvent::class => [
            WebhookEventListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
